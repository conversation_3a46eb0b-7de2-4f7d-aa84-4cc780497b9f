import { NextRequest, NextResponse } from 'next/server';
import { apiService } from '../../../services/api';
import Logger from '../../../utils/logger';

export async function GET(request: NextRequest) {
  try {
    Logger.log('📊 Reports API route called');
    
    // Extract query parameters
    const { searchParams } = new URL(request.url);
    const params: any = {};
    
    if (searchParams.get('report_type')) params.report_type = searchParams.get('report_type');
    if (searchParams.get('status')) params.status = searchParams.get('status');
    if (searchParams.get('limit')) params.limit = parseInt(searchParams.get('limit')!);
    if (searchParams.get('offset')) params.offset = parseInt(searchParams.get('offset')!);

    // Use the main API service which handles fallback to mock data
    const result = await apiService.getReports(params);
    
    return NextResponse.json(result);
  } catch (error) {
    Logger.error('Failed to fetch reports:', error);
    
    // Return error response
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch reports' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    Logger.log('📊 Create report API route called');
    
    const body = await request.json();
    
    // Use the main API service which handles fallback to mock data
    const result = await apiService.createReport(body);
    
    return NextResponse.json(result);
  } catch (error) {
    Logger.error('Failed to create report:', error);
    
    // Return error response
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create report' 
      },
      { status: 500 }
    );
  }
}
