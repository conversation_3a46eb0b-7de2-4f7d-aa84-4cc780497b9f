// Test script to verify the New Report button functionality
// This script can be run in the browser console to test the button

console.log('🧪 Testing New Report Button Functionality');

// Function to test if the button exists and is clickable
function testNewReportButton() {
  console.log('🔍 Looking for New Report button...');
  
  // Look for the button by text content
  const buttons = Array.from(document.querySelectorAll('button'));
  const newReportButton = buttons.find(btn => 
    btn.textContent.includes('New Report') || 
    btn.textContent.includes('Create Report')
  );
  
  if (!newReportButton) {
    console.error('❌ New Report button not found!');
    console.log('Available buttons:', buttons.map(btn => btn.textContent));
    return false;
  }
  
  console.log('✅ New Report button found:', newReportButton);
  
  // Check if button is enabled
  if (newReportButton.disabled) {
    console.error('❌ New Report button is disabled!');
    return false;
  }
  
  console.log('✅ New Report button is enabled');
  
  // Test clicking the button
  console.log('🖱️ Simulating button click...');
  
  try {
    newReportButton.click();
    console.log('✅ Button click executed successfully');
    
    // Wait a moment and check if modal appeared
    setTimeout(() => {
      const modal = document.querySelector('[role="dialog"]') || 
                   document.querySelector('.modal') ||
                   document.querySelector('[class*="modal"]') ||
                   document.querySelector('[class*="Modal"]');
      
      if (modal) {
        console.log('✅ Modal appeared after button click:', modal);
        
        // Look for modal content
        const modalTitle = modal.querySelector('h1, h2, h3, [class*="title"]');
        if (modalTitle) {
          console.log('✅ Modal title found:', modalTitle.textContent);
        }
        
        // Look for form elements
        const formElements = modal.querySelectorAll('input, select, textarea');
        console.log(`✅ Found ${formElements.length} form elements in modal`);
        
        // Look for close button
        const closeButton = modal.querySelector('[aria-label*="close"], [class*="close"], button[type="button"]');
        if (closeButton) {
          console.log('✅ Close button found in modal');
        }
        
        return true;
      } else {
        console.error('❌ No modal appeared after button click');
        console.log('🔍 Checking for any new elements...');
        
        // Check if any new elements appeared
        const allModals = document.querySelectorAll('div[class*="modal"], div[class*="Modal"], div[class*="dialog"], div[class*="Dialog"]');
        console.log('Found modal-like elements:', allModals.length);
        allModals.forEach((el, i) => {
          console.log(`Modal ${i}:`, el.className, el.style.display);
        });
        
        return false;
      }
    }, 1000);
    
  } catch (error) {
    console.error('❌ Error clicking button:', error);
    return false;
  }
}

// Function to test the reports page navigation
function testReportsPageNavigation() {
  console.log('🔍 Testing reports page navigation...');
  
  // Check if we're on the reports tab
  const url = window.location.href;
  if (url.includes('tab=reports')) {
    console.log('✅ Already on reports tab');
  } else {
    console.log('🔄 Navigating to reports tab...');
    window.location.href = window.location.origin + '/dashboard?tab=reports';
    return;
  }
  
  // Check if reports manager is loaded
  const reportsContainer = document.querySelector('[class*="reports"], [class*="Reports"]') ||
                          document.querySelector('div:has(button:contains("New Report"))');
  
  if (reportsContainer) {
    console.log('✅ Reports container found');
  } else {
    console.log('❌ Reports container not found');
  }
}

// Main test function
function runNewReportButtonTest() {
  console.log('🚀 Starting New Report Button Test');
  console.log('Current URL:', window.location.href);
  
  // First check if we're on the right page
  testReportsPageNavigation();
  
  // Wait a moment for page to load, then test the button
  setTimeout(() => {
    testNewReportButton();
  }, 2000);
}

// Auto-run the test
runNewReportButtonTest();

// Export functions for manual testing
window.testNewReportButton = testNewReportButton;
window.testReportsPageNavigation = testReportsPageNavigation;
window.runNewReportButtonTest = runNewReportButtonTest;

console.log('🔧 Test functions available:');
console.log('- testNewReportButton()');
console.log('- testReportsPageNavigation()');
console.log('- runNewReportButtonTest()');
