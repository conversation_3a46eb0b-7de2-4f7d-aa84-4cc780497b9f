# DReflowPro ETL Platform - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application Environment (development, staging, production)
APP_ENV=production

# Application Security
SECRET_KEY=CHANGE_ME_YOUR_SECRET_KEY_MIN_32_CHARS
JWT_SECRET_KEY=CHANGE_ME_YOUR_JWT_SECRET_KEY
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Settings (comma-separated list of allowed origins)
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
POSTGRES_USER=dreflowpro
POSTGRES_PASSWORD=CHANGE_ME_DATABASE_PASSWORD
POSTGRES_DB=dreflowpro
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Database URL (constructed from above values)
DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Cache and Sessions
REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================

# File Upload Configuration
UPLOAD_FOLDER=/app/uploads
MAX_UPLOAD_SIZE=100MB
ALLOWED_EXTENSIONS=csv,json,xlsx,xml,txt,parquet,pdf,doc,docx

# =============================================================================
# DOMAIN AND SSL SETTINGS
# =============================================================================

# Your domain name (used for SSL certificates and CORS)
DOMAIN=yourdomain.com

# SSL Certificate Email (for Let's Encrypt)
SSL_EMAIL=<EMAIL>

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================

# Grafana Admin Password
GRAFANA_PASSWORD=CHANGE_ME_GRAFANA_PASSWORD

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

# SMTP Settings for email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=CHANGE_ME_SMTP_PASSWORD
SMTP_TLS=true
SMTP_SSL=false

# Email Settings
FROM_EMAIL=<EMAIL>
FROM_NAME=DReflowPro ETL Platform

# =============================================================================
# EXTERNAL SERVICES (Optional)
# =============================================================================

# AWS S3 Configuration (for file storage)
AWS_ACCESS_KEY_ID=CHANGE_ME_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=CHANGE_ME_AWS_SECRET_KEY
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# =============================================================================
# OAUTH CONFIGURATION (Optional)
# =============================================================================

# OAuth Providers
GOOGLE_CLIENT_ID=CHANGE_ME_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=CHANGE_ME_GOOGLE_CLIENT_SECRET

GITHUB_CLIENT_ID=CHANGE_ME_GITHUB_CLIENT_ID
GITHUB_CLIENT_SECRET=CHANGE_ME_GITHUB_CLIENT_SECRET

MICROSOFT_CLIENT_ID=CHANGE_ME_MICROSOFT_CLIENT_ID
MICROSOFT_CLIENT_SECRET=CHANGE_ME_MICROSOFT_CLIENT_SECRET

# OAuth Redirect URL
OAUTH_REDIRECT_URL=https://${DOMAIN}/auth/oauth/{provider}/callback

# =============================================================================
# API RATE LIMITING
# =============================================================================

# Rate Limiting Configuration
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=20

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log Format (simple, detailed, json)
LOG_FORMAT=json

# Debug Mode (DO NOT enable in production)
DEBUG=false

# =============================================================================
# MULTI-TENANCY SETTINGS
# =============================================================================

# Default tenant settings
DEFAULT_TENANT_NAME=Default Organization
DEFAULT_TENANT_PLAN=professional
DEFAULT_TENANT_MAX_USERS=50
DEFAULT_TENANT_MAX_PIPELINES=100

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# Webhook Settings
WEBHOOK_SECRET=CHANGE_ME_WEBHOOK_SECRET
WEBHOOK_TIMEOUT_SECONDS=30
WEBHOOK_RETRY_ATTEMPTS=3

# =============================================================================
# BACKGROUND JOBS
# =============================================================================

# Celery Configuration (if using Celery instead of FastAPI BackgroundTasks)
CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/1
CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/2

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Security Headers
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_CONTENT_TYPE_OPTIONS=nosniff
SECURITY_FRAME_OPTIONS=SAMEORIGIN
SECURITY_XSS_PROTECTION=1; mode=block

# Password Policy
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=false

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles
FEATURE_MULTI_TENANCY=true
FEATURE_WEBSOCKETS=true
FEATURE_ANALYTICS=true
FEATURE_ADVANCED_EXPORTS=true
FEATURE_EMAIL_NOTIFICATIONS=true

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Connection Pool Settings
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_RECYCLE=3600

# Cache Settings
CACHE_DEFAULT_TIMEOUT=300
CACHE_LONG_TIMEOUT=3600
CACHE_REDIS_TIMEOUT=1800